<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::get('/sobre', function () {
    return Inertia::render('sobre');
})->name('sobre');

Route::get('/servicos', function () {
    return Inertia::render('servicos');
})->name('servicos');



Route::get('/contato', [App\Http\Controllers\ContatoController::class, 'index'])->name('contato');
Route::post('/contato', [App\Http\Controllers\ContatoController::class, 'store'])
    ->middleware(['sanitize.input', 'form.rate.limit:5,1'])
    ->name('contato.store');
Route::get('/contato/sucesso', [App\Http\Controllers\ContatoController::class, 'sucesso'])->name('contato.sucesso');

Route::get('/planos', function () {
    return Inertia::render('planos-publicos');
})->name('planos-publicos');

Route::get('/afiliados', function () {
    return Inertia::render('afiliados');
})->name('afiliados');

// Rotas de debug para afiliados (apenas em desenvolvimento)
if (app()->environment(['local', 'development'])) {
    Route::prefix('debug/affiliate')->group(function () {
        Route::get('/', [App\Http\Controllers\Debug\AffiliateDebugController::class, 'index'])->name('debug.affiliate');
        Route::post('/clear', [App\Http\Controllers\Debug\AffiliateDebugController::class, 'clear'])->name('debug.affiliate.clear');
        Route::post('/simulate-sale', [App\Http\Controllers\Debug\AffiliateDebugController::class, 'simulateSale'])->name('debug.affiliate.simulate');
    });
}

Route::get('/politica-privacidade', function () {
    return Inertia::render('politica-privacidade');
})->name('politica-privacidade');

Route::get('/termos-uso', function () {
    return Inertia::render('termos-uso');
})->name('termos-uso');

Route::get('/faq', function () {
    return Inertia::render('faq');
})->name('faq');

// Rota de cadastro de afiliados removida - agora é feito através das dashboards dos usuários

Route::get('/materiais-divulgacao', function () {
    return Inertia::render('materiais-divulgacao');
})->name('materiais-divulgacao');

// Admin routes for affiliates
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('afiliados', App\Http\Controllers\Admin\AfiliadoController::class);
    Route::post('afiliados/{afiliado}/aprovar', [App\Http\Controllers\Admin\AfiliadoController::class, 'aprovar'])->name('afiliados.aprovar');
    Route::post('afiliados/{afiliado}/rejeitar', [App\Http\Controllers\Admin\AfiliadoController::class, 'rejeitar'])->name('afiliados.rejeitar');
    Route::post('afiliados/{afiliado}/suspender', [App\Http\Controllers\Admin\AfiliadoController::class, 'suspender'])->name('afiliados.suspender');
    Route::post('afiliados/{afiliado}/reativar', [App\Http\Controllers\Admin\AfiliadoController::class, 'reativar'])->name('afiliados.reativar');
    Route::post('afiliados/{afiliado}/gerar-novo-link', [App\Http\Controllers\Admin\AfiliadoController::class, 'gerarNovoLink'])->name('afiliados.gerar-novo-link');

    // Cupons
    Route::resource('cupons', App\Http\Controllers\Admin\CupomController::class);
    Route::post('cupons/{cupom}/toggle-status', [App\Http\Controllers\Admin\CupomController::class, 'toggleStatus'])->name('cupons.toggle-status');

    // Pagamentos de Comissão
    Route::resource('pagamentos-comissao', App\Http\Controllers\Admin\PagamentoComissaoController::class)->only(['index', 'show']);
    Route::post('pagamentos-comissao/{pagamento}/aprovar', [App\Http\Controllers\Admin\PagamentoComissaoController::class, 'aprovar'])->name('pagamentos-comissao.aprovar');
    Route::post('pagamentos-comissao/{pagamento}/rejeitar', [App\Http\Controllers\Admin\PagamentoComissaoController::class, 'rejeitar'])->name('pagamentos-comissao.rejeitar');
    Route::post('pagamentos-comissao/{pagamento}/marcar-como-pago', [App\Http\Controllers\Admin\PagamentoComissaoController::class, 'marcarComoPago'])->name('pagamentos-comissao.marcar-como-pago');
    Route::post('pagamentos-comissao/bulk-action', [App\Http\Controllers\Admin\PagamentoComissaoController::class, 'bulkAction'])->name('pagamentos-comissao.bulk-action');
});

Route::get('/buscar', function () {
    return Inertia::render('buscar');
})->name('buscar');

// API para busca de estabelecimentos
Route::post('/api/estabelecimentos/buscar', [App\Http\Controllers\EstabelecimentoController::class, 'buscar'])->name('estabelecimentos.buscar');
Route::get('/api/estabelecimentos/categorias', [App\Http\Controllers\EstabelecimentoController::class, 'categorias'])->name('estabelecimentos.categorias');

// Webhook do Mercado Pago (sem middleware de autenticação)
Route::post('/webhook/mercadopago', [App\Http\Controllers\MercadoPagoWebhookController::class, 'handle'])->name('mercadopago.webhook');

// Rota de checkout (redirecionamento após pagamento)
Route::get('/checkout', function () {
    return redirect()->route('paciente.pagamentos.success');
})->name('checkout');





// Rotas para empresas
Route::get('/empresa/cadastro', [App\Http\Controllers\EmpresaController::class, 'index'])->name('empresa.cadastro');
Route::post('/empresa/cadastro', [App\Http\Controllers\EmpresaController::class, 'store'])->name('empresa.store');
Route::post('/empresa/{estabelecimento}/ativar-plano', [App\Http\Controllers\EmpresaController::class, 'ativarPlano'])->name('empresa.ativar-plano');

// Páginas SEO individuais de empresas (movido para depois das rotas autenticadas)

// Páginas SEO individuais de estabelecimentos
Route::get('/estabelecimento/{slug}', [App\Http\Controllers\EstabelecimentoPublicoController::class, 'show'])->name('estabelecimento.show');

Route::middleware(['auth', 'verified'])->group(function () {
    // Rota para alternar entre modos
    Route::post('switch-mode', [App\Http\Controllers\UserModeController::class, 'switch'])->name('switch.mode');

    // Rota para cadastro de afiliados (apenas usuários logados)
    Route::post('/afiliados/cadastro', [App\Http\Controllers\AfiliadoController::class, 'store'])
        ->middleware(['sanitize.input', 'form.rate.limit:3,1'])
        ->name('afiliados.store');

    Route::get('dashboard', function () {
        $user = auth()->user();
        $currentMode = session('user_mode', 'normal');

        // Se estiver em modo afiliado e tiver perfil de afiliado aprovado
        if ($currentMode === 'afiliado' && $user->canSwitchToAfiliadoMode()) {
            return redirect()->route('afiliado.dashboard');
        }

        // Redirecionar baseado no role do usuário
        switch ($user->role) {
            case 'admin':
                return redirect()->route('admin.dashboard');
            case 'fisioterapeuta':
                return redirect()->route('fisioterapeuta.dashboard');
            case 'paciente':
                // Fluxo: onboarding → planos → checkout → dashboard

                // 1. Verificar se completou onboarding
                if (!$user->onboarding_completed) {
                    return redirect()->route('paciente.onboarding')
                        ->with('info', 'Complete seu perfil médico para continuar.');
                }

                // 2. Verificar se selecionou plano
                if (!$user->plan_selected) {
                    return redirect()->route('paciente.planos')
                        ->with('info', 'Escolha um plano para continuar.');
                }

                // 3. Verificar se completou checkout
                if (!$user->checkout_completed) {
                    return redirect()->route('paciente.checkout')
                        ->with('info', 'Finalize seu plano para acessar a plataforma.');
                }

                // 4. Verificar se tem assinatura ativa (após checkout)
                if (!$user->has_subscription) {
                    return redirect()->route('paciente.checkout')
                        ->with('warning', 'Sua assinatura ainda não foi ativada. Complete o pagamento.');
                }

                return redirect()->route('paciente.dashboard');
            case 'afiliado':
                return redirect()->route('afiliado.dashboard');
            case 'empresa':
                return redirect()->route('empresa.dashboard');
            default:
                return redirect()->route('login');
        }
    })->name('dashboard');

    // Notificações
    Route::get('/notificacoes', [App\Http\Controllers\NotificacaoController::class, 'index'])->name('notificacoes.index');
    Route::get('/notificacoes/nao-lidas', [App\Http\Controllers\NotificacaoController::class, 'naoLidas'])->name('notificacoes.nao-lidas');
    Route::get('/notificacoes/{notificacao}', [App\Http\Controllers\NotificacaoController::class, 'show'])->name('notificacoes.show');
    Route::post('/notificacoes/{notificacao}/marcar-lida', [App\Http\Controllers\NotificacaoController::class, 'marcarComoLida'])->name('notificacoes.marcar-lida');
    Route::post('/notificacoes/marcar-todas-lidas', [App\Http\Controllers\NotificacaoController::class, 'marcarTodasComoLidas'])->name('notificacoes.marcar-todas-lidas');
    Route::delete('/notificacoes/{notificacao}', [App\Http\Controllers\NotificacaoController::class, 'destroy'])->name('notificacoes.destroy');

    // Rotas de Mensagens
    Route::prefix('mensagens')->name('mensagens.')->group(function () {
        Route::get('/', [App\Http\Controllers\MensagemController::class, 'index'])->name('index');
        Route::get('/buscar-usuarios', [App\Http\Controllers\MensagemController::class, 'buscarUsuarios'])->name('buscar-usuarios');
        Route::get('/contar-nao-lidas', [App\Http\Controllers\MensagemController::class, 'contarNaoLidas'])->name('contar-nao-lidas');
        Route::post('/', [App\Http\Controllers\MensagemController::class, 'store'])->name('store');
        Route::get('/{usuario}', [App\Http\Controllers\MensagemController::class, 'show'])->name('show');
        Route::patch('/{mensagem}/lida', [App\Http\Controllers\MensagemController::class, 'marcarLida'])->name('marcar-lida');
    });

    // Rotas de Avaliações
    Route::prefix('avaliacoes')->name('avaliacoes.')->group(function () {
        Route::get('/create', [App\Http\Controllers\AvaliacaoController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\AvaliacaoController::class, 'store'])->name('store');
        Route::get('/minhas', [App\Http\Controllers\AvaliacaoController::class, 'minhas'])->name('minhas');
        Route::get('/recebidas', [App\Http\Controllers\AvaliacaoController::class, 'recebidas'])->name('recebidas');
        Route::get('/{agendamento}/pode-avaliar', [App\Http\Controllers\AvaliacaoController::class, 'podeAvaliar'])->name('pode-avaliar');
        Route::get('/{avaliacao}', [App\Http\Controllers\AvaliacaoController::class, 'show'])->name('show');
    });

    // Rotas de Status do Perfil
    Route::prefix('profile-status')->name('profile-status.')->group(function () {
        Route::get('/status', [App\Http\Controllers\ProfileStatusController::class, 'status'])->name('status');
        Route::get('/analysis', [App\Http\Controllers\ProfileStatusController::class, 'analysis'])->name('analysis');
        Route::get('/check', [App\Http\Controllers\ProfileStatusController::class, 'check'])->name('check');
        Route::get('/suggestions', [App\Http\Controllers\ProfileStatusController::class, 'suggestions'])->name('suggestions');
    });
});

// Rotas do Administrador
Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');

    Route::resource('usuarios', App\Http\Controllers\Admin\UsuarioController::class);
    Route::get('usuarios/{usuario}/history', [App\Http\Controllers\Admin\UsuarioController::class, 'history'])->name('usuarios.history');
    Route::get('usuarios-export', [App\Http\Controllers\Admin\UsuarioController::class, 'export'])->name('usuarios.export');
    Route::resource('fisioterapeutas', App\Http\Controllers\Admin\FisioterapeutaController::class);
    Route::resource('planos', App\Http\Controllers\Admin\PlanoController::class);
    Route::resource('estabelecimentos', App\Http\Controllers\Admin\EstabelecimentoController::class);
    Route::post('estabelecimentos/{estabelecimento}/toggle-ativo', [App\Http\Controllers\Admin\EstabelecimentoController::class, 'toggleAtivo'])->name('estabelecimentos.toggle-ativo');
    Route::post('estabelecimentos/{estabelecimento}/toggle-plano', [App\Http\Controllers\Admin\EstabelecimentoController::class, 'togglePlano'])->name('estabelecimentos.toggle-plano');

    // Pagamentos
    Route::get('/pagamentos', [App\Http\Controllers\Admin\PagamentoController::class, 'index'])->name('pagamentos.index');
    Route::get('/pagamentos/{pagamento}', [App\Http\Controllers\Admin\PagamentoController::class, 'show'])->name('pagamentos.show');
    Route::post('/pagamentos/{pagamento}/mark-as-paid', [App\Http\Controllers\Admin\PagamentoController::class, 'markAsPaid'])->name('pagamentos.mark-as-paid');
    Route::post('/pagamentos/{pagamento}/mark-as-failed', [App\Http\Controllers\Admin\PagamentoController::class, 'markAsFailed'])->name('pagamentos.mark-as-failed');
    Route::post('/pagamentos/{pagamento}/cancel', [App\Http\Controllers\Admin\PagamentoController::class, 'cancel'])->name('pagamentos.cancel');
    Route::put('/pagamentos/{pagamento}', [App\Http\Controllers\Admin\PagamentoController::class, 'update'])->name('pagamentos.update');
    Route::post('/assinaturas/{assinatura}/generate-payment', [App\Http\Controllers\Admin\PagamentoController::class, 'generatePayment'])->name('pagamentos.generate');
    Route::get('/pagamentos/export', [App\Http\Controllers\Admin\PagamentoController::class, 'export'])->name('pagamentos.export');



    // Rotas de Relatórios
    Route::get('/relatorios', [App\Http\Controllers\Admin\RelatorioController::class, 'index'])->name('relatorios.index');
    Route::get('/relatorios/financeiro', [App\Http\Controllers\Admin\RelatorioController::class, 'financeiro'])->name('relatorios.financeiro');
    Route::get('/relatorios/operacional', [App\Http\Controllers\Admin\RelatorioController::class, 'operacional'])->name('relatorios.operacional');
    Route::get('/relatorios/pacientes', [App\Http\Controllers\Admin\RelatorioController::class, 'pacientes'])->name('relatorios.pacientes');
    Route::get('/relatorios/fisioterapeutas', [App\Http\Controllers\Admin\RelatorioController::class, 'fisioterapeutas'])->name('relatorios.fisioterapeutas');
    Route::get('/relatorios/export', [App\Http\Controllers\Admin\RelatorioController::class, 'export'])->name('relatorios.export');

    // Rotas de Backup e Exportação
    Route::prefix('backup')->name('backup.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\BackupController::class, 'index'])->name('index');
        Route::post('/create', [App\Http\Controllers\Admin\BackupController::class, 'createBackup'])->name('create');
        Route::get('/download/{backupId}', [App\Http\Controllers\Admin\BackupController::class, 'downloadBackup'])->name('download');
        Route::delete('/delete/{backupId}', [App\Http\Controllers\Admin\BackupController::class, 'deleteBackup'])->name('delete');
        Route::post('/cleanup', [App\Http\Controllers\Admin\BackupController::class, 'cleanupOldBackups'])->name('cleanup');
        Route::post('/auto-config', [App\Http\Controllers\Admin\BackupController::class, 'configureAutoBackup'])->name('auto-config');
        Route::get('/auto-config', [App\Http\Controllers\Admin\BackupController::class, 'getAutoBackupConfig'])->name('get-auto-config');
    });

    // Rotas de Exportação
    Route::prefix('export')->name('export.')->group(function () {
        Route::post('/report', [App\Http\Controllers\Admin\BackupController::class, 'exportReport'])->name('report');
        Route::get('/download/{exportId}', [App\Http\Controllers\Admin\BackupController::class, 'downloadExport'])->name('download');
        Route::delete('/delete/{exportId}', [App\Http\Controllers\Admin\BackupController::class, 'deleteExport'])->name('delete');
    });

    // Rotas de Importação
    Route::prefix('import')->name('import.')->group(function () {
        Route::post('/upload', [App\Http\Controllers\Admin\BackupController::class, 'uploadImportFile'])->name('upload');
        Route::post('/process', [App\Http\Controllers\Admin\BackupController::class, 'processImport'])->name('process');
        Route::get('/template/{type}', [App\Http\Controllers\Admin\BackupController::class, 'generateTemplate'])->name('template');
    });
});

// Rotas do Fisioterapeuta
Route::middleware(['auth', 'verified', 'role:fisioterapeuta'])->prefix('fisioterapeuta')->name('fisioterapeuta.')->group(function () {
    // Setup inicial (sem middleware check.fisioterapeuta)
    Route::get('/setup', [App\Http\Controllers\Fisioterapeuta\SetupController::class, 'index'])->name('setup');
    Route::post('/setup', [App\Http\Controllers\Fisioterapeuta\SetupController::class, 'store'])->name('setup.store');

    // Perfil (sempre acessível para permitir completar dados)
    Route::get('/perfil', [App\Http\Controllers\Fisioterapeuta\PerfilController::class, 'index'])->name('perfil');
    Route::put('/perfil', [App\Http\Controllers\Fisioterapeuta\PerfilController::class, 'update'])->name('perfil.update');
    Route::post('/perfil/avatar', [App\Http\Controllers\Fisioterapeuta\PerfilController::class, 'uploadAvatar'])->name('perfil.avatar.upload');
    Route::delete('/perfil/avatar', [App\Http\Controllers\Fisioterapeuta\PerfilController::class, 'removeAvatar'])->name('perfil.avatar.remove');

    // Rotas que requerem perfil completo e verificação adicional
    Route::middleware(['check.fisioterapeuta', 'check.profile:enforce'])->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Fisioterapeuta\DashboardController::class, 'index'])->name('dashboard');

    // Agenda
    Route::get('/agenda', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'index'])->name('agenda.index');
    Route::get('/agenda/{agendamento}', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'show'])->name('agenda.show');
    Route::post('/agenda/{agendamento}/confirmar', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'confirmar'])->name('agenda.confirmar');
    Route::post('/agenda/{agendamento}/aceitar', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'aceitar'])->name('agenda.aceitar');
    Route::post('/agenda/{agendamento}/recusar', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'recusar'])->name('agenda.recusar');
    Route::post('/agenda/{agendamento}/reagendar', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'reagendar'])->name('agenda.reagendar');
    Route::post('/agenda/{agendamento}/iniciar', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'iniciar'])->name('agenda.iniciar');
    Route::post('/agenda/{agendamento}/finalizar', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'finalizar'])->name('agenda.finalizar');
    Route::post('/agenda/{agendamento}/cancelar', [App\Http\Controllers\Fisioterapeuta\AgendaController::class, 'cancelar'])->name('agenda.cancelar');

    // Pacientes
    Route::get('/pacientes', [App\Http\Controllers\Fisioterapeuta\PacienteController::class, 'index'])->name('pacientes.index');
    Route::get('/pacientes/{paciente}', [App\Http\Controllers\Fisioterapeuta\PacienteController::class, 'show'])->name('pacientes.show');
    Route::post('/pacientes/{paciente}/notas', [App\Http\Controllers\Fisioterapeuta\PacienteController::class, 'notas'])->name('pacientes.notas');

    // Disponibilidade
    Route::resource('disponibilidade', App\Http\Controllers\Fisioterapeuta\DisponibilidadeController::class);
    Route::post('/disponibilidade/{disponibilidade}/toggle', [App\Http\Controllers\Fisioterapeuta\DisponibilidadeController::class, 'toggle'])->name('disponibilidade.toggle');

    // Configuração de Horários Customizados
    Route::prefix('horarios')->name('horarios.')->group(function () {
        Route::get('/', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'index'])->name('index');

        // Horários Base
        Route::post('base', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'storeHorarioBase'])->name('base.store');
        Route::put('base/{horarioBase}', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'updateHorarioBase'])->name('base.update');
        Route::delete('base/{horarioBase}', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'destroyHorarioBase'])->name('base.destroy');

        // Exceções
        Route::post('excecoes', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'storeExcecao'])->name('excecoes.store');
        Route::put('excecoes/{excecao}', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'updateExcecao'])->name('excecoes.update');
        Route::delete('excecoes/{excecao}', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'destroyExcecao'])->name('excecoes.destroy');

        // Configuração de Feriados
        Route::put('feriados', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'updateConfigFeriados'])->name('feriados.update');

        // Preview e Utilitários
        Route::post('preview', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'preview'])->name('preview');
        Route::get('feriados', [App\Http\Controllers\Fisioterapeuta\HorarioConfigController::class, 'feriados'])->name('feriados.list');
    });

    // Relatórios
    Route::get('/relatorios', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'index'])->name('relatorios.index');
    Route::get('/relatorios/pendentes', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'pendentes'])->name('relatorios.pendentes');
    Route::get('/relatorios/create/{agendamento}', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'create'])->name('relatorios.create');
    Route::post('/relatorios/{agendamento}', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'store'])->name('relatorios.store');
    Route::get('/relatorios/{relatorio}', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'show'])->name('relatorios.show');
    Route::get('/relatorios/{relatorio}/edit', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'edit'])->name('relatorios.edit');
    Route::put('/relatorios/{relatorio}', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'update'])->name('relatorios.update');
    Route::post('/relatorios/{relatorio}/anexos', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'uploadAnexo'])->name('relatorios.anexos.upload');
    Route::delete('/relatorios/anexos/{anexo}', [App\Http\Controllers\Fisioterapeuta\RelatorioController::class, 'removeAnexo'])->name('relatorios.anexos.remove');

    // Avaliações
    Route::get('/avaliacoes', [App\Http\Controllers\AvaliacaoController::class, 'recebidas'])->name('avaliacoes.index');



        // Programa de Afiliados
        Route::get('/afiliados', function () {
            return Inertia::render('fisioterapeuta/afiliados');
        })->name('afiliados');
    });
});

// Rotas do Afiliado
Route::middleware(['auth', 'verified', 'check.afiliado'])->prefix('afiliado')->name('afiliado.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Afiliado\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/vendas', [App\Http\Controllers\Afiliado\VendasController::class, 'index'])->name('vendas');
    Route::get('/materiais', [App\Http\Controllers\Afiliado\MateriaisController::class, 'index'])->name('materiais');
    Route::get('/perfil', [App\Http\Controllers\Afiliado\PerfilController::class, 'index'])->name('perfil');
    Route::put('/perfil', [App\Http\Controllers\Afiliado\PerfilController::class, 'update'])->name('perfil.update');

    // Cupons
    Route::get('/cupons', [App\Http\Controllers\Afiliado\CupomController::class, 'index'])->name('cupons.index');
    Route::get('/cupons/{cupom}', [App\Http\Controllers\Afiliado\CupomController::class, 'show'])->name('cupons.show');
    Route::get('/cupons/{cupom}/link', [App\Http\Controllers\Afiliado\CupomController::class, 'getLink'])->name('cupons.link');

    // Pagamentos de Comissão
    Route::get('/pagamentos', [App\Http\Controllers\Afiliado\PagamentoComissaoController::class, 'index'])->name('pagamentos.index');
    Route::get('/pagamentos/create', [App\Http\Controllers\Afiliado\PagamentoComissaoController::class, 'create'])->name('pagamentos.create');
    Route::post('/pagamentos', [App\Http\Controllers\Afiliado\PagamentoComissaoController::class, 'store'])->name('pagamentos.store');
    Route::get('/pagamentos/{pagamento}', [App\Http\Controllers\Afiliado\PagamentoComissaoController::class, 'show'])->name('pagamentos.show');
});

// Rotas do Paciente
Route::middleware(['auth', 'verified', 'role:paciente'])->prefix('paciente')->name('paciente.')->group(function () {
    // Seleção de planos (sem middleware de verificação de assinatura)
    Route::get('/planos', [App\Http\Controllers\Paciente\PlanosController::class, 'index'])->name('planos');
    Route::post('/planos', [App\Http\Controllers\Paciente\PlanosController::class, 'store'])->name('planos.store');
    Route::get('/planos/mercadopago-status', [App\Http\Controllers\Paciente\PlanosController::class, 'checkMercadoPagoStatus'])->name('planos.mercadopago-status');

    // Onboarding médico (sem middleware check.paciente.medical)
    Route::get('/onboarding', [App\Http\Controllers\Paciente\OnboardingController::class, 'index'])->name('onboarding');
    Route::post('/onboarding', [App\Http\Controllers\Paciente\OnboardingController::class, 'store'])->name('onboarding.store');
    Route::post('/onboarding/save-partial', [App\Http\Controllers\Paciente\OnboardingController::class, 'savePartial'])->name('onboarding.save-partial');

    // Programa de Afiliados (sem middleware de assinatura)
    Route::get('/afiliados', function () {
        return Inertia::render('paciente/afiliados');
    })->name('afiliados');

    // Checkout (sem middleware de assinatura)
    Route::get('/checkout', [App\Http\Controllers\Paciente\CheckoutController::class, 'index'])->name('checkout');
    Route::post('/checkout', [App\Http\Controllers\Paciente\CheckoutController::class, 'process'])->name('checkout.process');
    Route::get('/checkout/simulation/{assinatura}', [App\Http\Controllers\Paciente\CheckoutController::class, 'simulation'])->name('checkout.simulation');
    Route::get('/checkout/success', [App\Http\Controllers\Paciente\CheckoutController::class, 'success'])->name('checkout.success');
    Route::post('/checkout/confirm', [App\Http\Controllers\Paciente\CheckoutController::class, 'confirm'])->name('checkout.confirm');

    // Rotas que requerem assinatura ativa, dados médicos completos e perfil básico
    Route::middleware(['check.subscription', 'check.paciente.medical', 'check.profile:80'])->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Paciente\DashboardController::class, 'index'])->name('dashboard');

        // Fisioterapeutas
        Route::get('/fisioterapeutas', [App\Http\Controllers\Paciente\FisioterapeutaController::class, 'index'])->name('fisioterapeutas.index');
        Route::get('/fisioterapeutas/{fisioterapeuta}', [App\Http\Controllers\Paciente\FisioterapeutaController::class, 'show'])->name('fisioterapeutas.show');

        Route::resource('agendamentos', App\Http\Controllers\Paciente\AgendamentoController::class)->except(['edit', 'update']);
        Route::post('/agendamentos/{agendamento}/cancel', [App\Http\Controllers\Paciente\AgendamentoController::class, 'cancel'])->name('agendamentos.cancel');
        Route::get('/agendamentos/{agendamento}/reschedule', [App\Http\Controllers\Paciente\AgendamentoController::class, 'reschedule'])->name('agendamentos.reschedule');
        Route::put('/agendamentos/{agendamento}/reschedule', [App\Http\Controllers\Paciente\AgendamentoController::class, 'updateReschedule'])->name('agendamentos.update-reschedule');
        Route::get('/agendamentos/{agendamento}/report', [App\Http\Controllers\Paciente\AgendamentoController::class, 'showReport'])->name('agendamentos.report');
        Route::get('/agendamentos/{agendamento}/evaluate', [App\Http\Controllers\Paciente\AgendamentoController::class, 'showEvaluation'])->name('agendamentos.evaluate');
        Route::post('/agendamentos/{agendamento}/evaluate', [App\Http\Controllers\Paciente\AgendamentoController::class, 'storeEvaluation'])->name('agendamentos.store-evaluation');

        // API de agendamento
        Route::post('/agendamentos/horarios-disponiveis', [App\Http\Controllers\Paciente\AgendamentoController::class, 'horariosDisponiveis'])->name('agendamentos.horarios-disponiveis');
        Route::post('/agendamentos/fisioterapeutas-disponiveis', [App\Http\Controllers\Paciente\AgendamentoController::class, 'fisioterapeutasDisponiveis'])->name('agendamentos.fisioterapeutas-disponiveis');
        Route::post('/agendamentos/verificar-disponibilidade', [App\Http\Controllers\Paciente\AgendamentoController::class, 'verificarDisponibilidadeApi'])->name('agendamentos.verificar-disponibilidade');

        // Histórico
        Route::get('/historico', [App\Http\Controllers\Paciente\HistoricoController::class, 'index'])->name('historico.index');
        Route::get('/historico/export', [App\Http\Controllers\Paciente\HistoricoController::class, 'export'])->name('historico.export');
        Route::get('/historico/{agendamento}', [App\Http\Controllers\Paciente\HistoricoController::class, 'show'])->name('historico.show');

        // Pagamentos
        Route::get('/pagamentos', [App\Http\Controllers\Paciente\PagamentoController::class, 'index'])->name('pagamentos.index');
        Route::get('/pagamentos/{pagamento}', [App\Http\Controllers\Paciente\PagamentoController::class, 'show'])->name('pagamentos.show');
        Route::post('/pagamentos/{pagamento}/process', [App\Http\Controllers\Paciente\PagamentoController::class, 'processPayment'])->name('pagamentos.process');
        Route::get('/pagamentos/{pagamento}/boleto', [App\Http\Controllers\Paciente\PagamentoController::class, 'generateSlip'])->name('pagamentos.boleto');
        Route::get('/pagamentos/{pagamento}/pix', [App\Http\Controllers\Paciente\PagamentoController::class, 'generatePix'])->name('pagamentos.pix');
        Route::get('/pagamentos/{pagamento}/comprovante', [App\Http\Controllers\Paciente\PagamentoController::class, 'downloadReceipt'])->name('pagamentos.comprovante');

        // Rotas de retorno do Mercado Pago
        Route::get('/pagamentos/success', [App\Http\Controllers\Paciente\PagamentoController::class, 'paymentSuccess'])->name('pagamentos.success');
        Route::get('/pagamentos/failure', [App\Http\Controllers\Paciente\PagamentoController::class, 'paymentFailure'])->name('pagamentos.failure');
        Route::get('/pagamentos/pending', [App\Http\Controllers\Paciente\PagamentoController::class, 'paymentPending'])->name('pagamentos.pending');

        // Perfil
        Route::get('/perfil', [App\Http\Controllers\Paciente\PerfilController::class, 'index'])->name('perfil');
        Route::put('/perfil', [App\Http\Controllers\Paciente\PerfilController::class, 'update'])->name('perfil.update');
        Route::post('/perfil/avatar', [App\Http\Controllers\Paciente\PerfilController::class, 'uploadAvatar'])->name('perfil.avatar.upload');
        Route::delete('/perfil/avatar', [App\Http\Controllers\Paciente\PerfilController::class, 'removeAvatar'])->name('perfil.avatar.remove');

        // Avaliações
        Route::get('/avaliacoes', [App\Http\Controllers\AvaliacaoController::class, 'minhas'])->name('avaliacoes.index');

        // Planos
        Route::get('/plano', [App\Http\Controllers\Paciente\PlanoController::class, 'index'])->name('plano.index');
        Route::post('/plano/subscribe', [App\Http\Controllers\Paciente\PlanoController::class, 'subscribe'])->name('plano.subscribe');
        Route::post('/plano/cancel', [App\Http\Controllers\Paciente\PlanoController::class, 'cancel'])->name('plano.cancel');
        Route::post('/plano/change', [App\Http\Controllers\Paciente\PlanoController::class, 'change'])->name('plano.change');
    });
});

// Teste simples fora do grupo
Route::middleware(['auth'])->get('/empresa/dashboard-simples', function () {
    return response()->json(['message' => 'Dashboard simples funcionando!', 'user' => auth()->user()->name]);
});

// Rotas da Empresa
Route::middleware(['auth', 'verified', 'role:empresa'])->prefix('empresa')->name('empresa.')->group(function () {
    // Setup inicial (sem middleware check.empresa)
    Route::get('/setup', [App\Http\Controllers\Empresa\SetupController::class, 'index'])->name('setup');
    Route::post('/setup', [App\Http\Controllers\Empresa\SetupController::class, 'store'])->name('setup.store');

    // Rotas que requerem estabelecimento configurado
    Route::middleware(['check.empresa'])->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Empresa\DashboardController::class, 'index'])->name('dashboard');
        Route::get('/perfil', [App\Http\Controllers\Empresa\PerfilController::class, 'index'])->name('perfil');
        Route::put('/perfil', [App\Http\Controllers\Empresa\PerfilController::class, 'update'])->name('perfil.update');
    });
});

// Páginas SEO individuais de empresas (deve vir por último para não conflitar)
Route::get('/empresa/{slug}', [App\Http\Controllers\EmpresaPublicaController::class, 'show'])->name('empresa.publica.show');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

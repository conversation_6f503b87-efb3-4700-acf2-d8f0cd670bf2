<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'phone',
        'avatar',
        'birth_date',
        'gender',
        'address',
        'city',
        'state',
        'zip_code',
        'medical_history',
        'emergency_contact',
        'active',
        'has_subscription',
        'onboarding_completed',
        'plan_selected',
        'checkout_completed',
        'onboarding_completed_at',
        'plan_selected_at',
        'checkout_completed_at',
        'privacy_profile_visible',
        'privacy_contact_visible',
        'privacy_medical_visible',
        'privacy_allow_marketing',
        'main_objective',
        'pain_level',
        'specific_areas',
        'treatment_goals',
        'preferred_time',
        'preferred_days',
        'communication_preference',
        'reminder_frequency',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'birth_date' => 'date',
            'active' => 'boolean',
            'has_subscription' => 'boolean',
            'onboarding_completed' => 'boolean',
            'plan_selected' => 'boolean',
            'checkout_completed' => 'boolean',
            'onboarding_completed_at' => 'datetime',
            'plan_selected_at' => 'datetime',
            'checkout_completed_at' => 'datetime',
            'privacy_profile_visible' => 'boolean',
            'privacy_contact_visible' => 'boolean',
            'privacy_medical_visible' => 'boolean',
            'privacy_allow_marketing' => 'boolean',
            'specific_areas' => 'array',
            'preferred_days' => 'array',
        ];
    }

    // Relacionamentos
    public function fisioterapeuta()
    {
        return $this->hasOne(Fisioterapeuta::class);
    }

    public function afiliado()
    {
        return $this->hasOne(Afiliado::class);
    }

    public function estabelecimentos()
    {
        return $this->hasMany(Estabelecimento::class);
    }

    public function estabelecimento()
    {
        return $this->hasOne(Estabelecimento::class);
    }

    public function assinaturas()
    {
        return $this->hasMany(Assinatura::class);
    }

    public function agendamentosComoPaciente()
    {
        return $this->hasMany(Agendamento::class, 'paciente_id');
    }

    public function agendamentosComoFisioterapeuta()
    {
        return $this->hasMany(Agendamento::class, 'fisioterapeuta_id');
    }

    public function avaliacoesRecebidas()
    {
        return $this->hasMany(Avaliacao::class, 'fisioterapeuta_id');
    }

    public function avaliacoesFeitas()
    {
        return $this->hasMany(Avaliacao::class, 'paciente_id');
    }

    // Relacionamentos para sistema de horários customizados
    public function horariosBase()
    {
        return $this->hasMany(HorarioBase::class, 'fisioterapeuta_id');
    }

    public function horariosExcecoes()
    {
        return $this->hasMany(HorarioExcecao::class, 'fisioterapeuta_id');
    }

    public function configFeriados()
    {
        return $this->hasOne(MedicoFeriadosConfig::class, 'fisioterapeuta_id');
    }

    // Scopes
    public function scopeAtivos($query)
    {
        return $query->where('active', true);
    }

    public function scopePorRole($query, $role)
    {
        return $query->where('role', $role);
    }

    // Métodos auxiliares
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isFisioterapeuta()
    {
        return $this->role === 'fisioterapeuta';
    }

    public function isPaciente()
    {
        return $this->role === 'paciente';
    }

    public function isAfiliado()
    {
        return $this->role === 'afiliado';
    }

    public function isEmpresa()
    {
        return $this->role === 'empresa';
    }

    public function hasAfiliadoProfile()
    {
        return $this->afiliado()->exists();
    }

    public function getAfiliadoProfile()
    {
        return $this->afiliado;
    }

    public function canSwitchToAfiliadoMode()
    {
        return $this->hasAfiliadoProfile() &&
               $this->afiliado->status === 'aprovado' &&
               $this->afiliado->ativo;
    }

    /**
     * Send the password reset notification.
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    // Relacionamentos para prescrições e orientações
    public function prescricoes()
    {
        return $this->hasMany(Prescricao::class, 'paciente_id');
    }

    public function orientacoesDomiciliares()
    {
        return $this->hasMany(OrientacaoDomiciliar::class, 'paciente_id');
    }
}

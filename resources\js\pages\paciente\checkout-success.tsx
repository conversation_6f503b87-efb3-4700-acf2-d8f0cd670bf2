import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PacienteLayout from '@/layouts/PacienteLayout';
import { Head, useForm, usePage } from '@inertiajs/react';
import { ArrowRight, Calendar, CheckCircle, FileText, Sparkles, Users } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Props {
    user: any;
    flash?: {
        simulation_url?: string;
    };
}

export default function CheckoutSuccess() {
    const pageProps = usePage().props as any;
    const { user } = pageProps;
    const [countdown, setCountdown] = useState(10);
    const [autoRedirect, setAutoRedirect] = useState(true);

    const { post } = useForm();

    useEffect(() => {
        if (!autoRedirect) return;

        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    handleConfirm();
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [autoRedirect]);

    const handleConfirm = () => {
        post(route('paciente.checkout.confirm'));
    };

    const handleStopAutoRedirect = () => {
        setAutoRedirect(false);
    };

    // Verificar se há URL de simulação na sessão
    const simulationUrl = pageProps.flash?.simulation_url;

    useEffect(() => {
        if (simulationUrl) {
            // Abrir simulação em nova aba
            window.open(simulationUrl, '_blank', 'width=600,height=800,scrollbars=yes,resizable=yes');
        }
    }, [simulationUrl]);

    return (
        <PacienteLayout>
            <Head title="Plano Ativado com Sucesso!" />

            <div className="container mx-auto max-w-4xl px-4 py-8">
                <div className="mb-8 text-center">
                    <div className="mx-auto mb-6 w-fit rounded-full bg-green-100 p-4">
                        <CheckCircle className="h-16 w-16 text-green-600" />
                    </div>
                    <h1 className="mb-2 text-4xl font-bold text-gray-900">Parabéns, {user.name}!</h1>
                    <p className="text-xl text-gray-600">Seu plano foi ativado com sucesso</p>
                </div>

                <div className="grid gap-8 lg:grid-cols-2">
                    {/* Confirmação */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Sparkles className="h-5 w-5 text-yellow-500" />
                                Plano Ativo
                            </CardTitle>
                            <CardDescription>Sua assinatura está ativa e você já pode começar a usar todos os benefícios</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                                <h3 className="mb-2 font-semibold text-green-800">✓ Tudo Pronto!</h3>
                                <ul className="space-y-1 text-sm text-green-700">
                                    <li>• Plano ativado com sucesso</li>
                                    <li>• Acesso liberado à plataforma</li>
                                    <li>• E-mail de confirmação enviado</li>
                                    <li>• Você pode começar a agendar sessões</li>
                                </ul>
                            </div>

                            {autoRedirect && (
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="font-medium text-blue-800">Redirecionamento automático</p>
                                            <p className="text-sm text-blue-600">Você será redirecionado em {countdown} segundos</p>
                                        </div>
                                        <Button variant="outline" size="sm" onClick={handleStopAutoRedirect}>
                                            Cancelar
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Próximos Passos */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Próximos Passos</CardTitle>
                            <CardDescription>Veja o que você pode fazer agora</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                <div className="flex items-start gap-3 rounded-lg border p-3 hover:bg-gray-50">
                                    <Calendar className="mt-0.5 h-5 w-5 text-blue-600" />
                                    <div>
                                        <h4 className="font-medium">Agendar sua primeira sessão</h4>
                                        <p className="text-sm text-gray-600">Encontre fisioterapeutas disponíveis na sua região</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-3 rounded-lg border p-3 hover:bg-gray-50">
                                    <Users className="mt-0.5 h-5 w-5 text-green-600" />
                                    <div>
                                        <h4 className="font-medium">Explorar fisioterapeutas</h4>
                                        <p className="text-sm text-gray-600">Veja perfis e especializações dos profissionais</p>
                                    </div>
                                </div>

                                <div className="flex items-start gap-3 rounded-lg border p-3 hover:bg-gray-50">
                                    <FileText className="mt-0.5 h-5 w-5 text-purple-600" />
                                    <div>
                                        <h4 className="font-medium">Completar seu perfil</h4>
                                        <p className="text-sm text-gray-600">Adicione mais informações para melhor atendimento</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Informações Importantes */}
                <Card className="mt-8">
                    <CardHeader>
                        <CardTitle>Informações Importantes</CardTitle>
                    </CardHeader>
                    <CardContent className="grid gap-6 md:grid-cols-2">
                        <div>
                            <h4 className="mb-2 font-semibold">📧 Confirmação por E-mail</h4>
                            <p className="text-sm text-gray-600">
                                Enviamos um e-mail de confirmação para <strong>{user.email}</strong>
                                com todos os detalhes da sua assinatura.
                            </p>
                        </div>

                        <div>
                            <h4 className="mb-2 font-semibold">💳 Cobrança</h4>
                            <p className="text-sm text-gray-600">Sua próxima cobrança será processada automaticamente no mesmo dia do próximo mês.</p>
                        </div>

                        <div>
                            <h4 className="mb-2 font-semibold">📞 Suporte</h4>
                            <p className="text-sm text-gray-600">
                                Precisa de ajuda? Nossa equipe está disponível 24/7 pelo WhatsApp ou através do chat na plataforma.
                            </p>
                        </div>

                        <div>
                            <h4 className="mb-2 font-semibold">🔄 Cancelamento</h4>
                            <p className="text-sm text-gray-600">
                                Você pode cancelar sua assinatura a qualquer momento através das configurações da sua conta.
                            </p>
                        </div>
                    </CardContent>
                </Card>

                {/* Botões de Ação */}
                <div className="mt-8 flex flex-col justify-center gap-4 sm:flex-row">
                    <Button onClick={handleConfirm} size="lg" className="px-8">
                        Ir para Dashboard
                        <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>

                    <Button variant="outline" size="lg" asChild>
                        <a href="https://wa.me/5511978196207" target="_blank" rel="noopener noreferrer">
                            Falar com Suporte
                        </a>
                    </Button>
                </div>

                {simulationUrl && (
                    <div className="mt-8 rounded-lg border border-yellow-200 bg-yellow-50 p-4 text-center">
                        <p className="text-yellow-800">
                            <strong>Modo Desenvolvimento:</strong> Uma simulação de pagamento foi aberta em nova aba para demonstração.
                        </p>
                    </div>
                )}
            </div>
        </PacienteLayout>
    );
}

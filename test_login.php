<?php

// Script simples para testar login via cURL
$baseUrl = 'http://localhost:8000';

// Prime<PERSON>, obter o token CSRF da página de login
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');

$loginPage = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "HTTP Code para /login: $httpCode\n";

if ($httpCode >= 300 && $httpCode < 400) {
    $location = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
    echo "Redirecionamento para: $location\n";
}

curl_close($ch);

// Se conseguiu acessar a página de login, extrair o token CSRF
if ($httpCode == 200 && $loginPage) {
    preg_match('/<meta name="csrf-token" content="([^"]+)"/', $loginPage, $matches);
    if (isset($matches[1])) {
        $csrfToken = $matches[1];
        echo "Token CSRF encontrado: $csrfToken\n";
        
        // Tentar fazer login
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            '_token' => $csrfToken,
            'email' => '<EMAIL>',
            'password' => 'password123',
            'remember' => false
        ]));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
        curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'X-CSRF-TOKEN: ' . $csrfToken
        ]);

        $loginResponse = curl_exec($ch);
        $loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        echo "HTTP Code para POST /login: $loginHttpCode\n";
        
        if ($loginHttpCode >= 300 && $loginHttpCode < 400) {
            $location = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            echo "Redirecionamento após login para: $location\n";
            
            // Seguir o redirecionamento para ver onde vai
            curl_setopt($ch, CURLOPT_URL, $location);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
            
            $dashboardResponse = curl_exec($ch);
            $dashboardHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            echo "HTTP Code para dashboard: $dashboardHttpCode\n";

            if ($dashboardHttpCode >= 300 && $dashboardHttpCode < 400) {
                $finalLocation = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
                echo "Redirecionamento final para: $finalLocation\n";

                // Seguir mais um redirecionamento para ver onde termina
                curl_setopt($ch, CURLOPT_URL, $finalLocation);
                $finalResponse = curl_exec($ch);
                $finalHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                echo "HTTP Code final: $finalHttpCode\n";

                if ($finalHttpCode == 200) {
                    echo "✅ Usuário conseguiu acessar: $finalLocation\n";
                } elseif ($finalHttpCode >= 300 && $finalHttpCode < 400) {
                    $nextLocation = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
                    echo "Outro redirecionamento para: $nextLocation\n";
                }
            } elseif ($dashboardHttpCode == 200) {
                echo "✅ Usuário conseguiu acessar o dashboard diretamente\n";
            }
        }
        
        curl_close($ch);
    } else {
        echo "Token CSRF não encontrado na página\n";
    }
} else {
    echo "Não foi possível acessar a página de login\n";
}

// Limpar arquivo de cookies
if (file_exists('cookies.txt')) {
    unlink('cookies.txt');
}

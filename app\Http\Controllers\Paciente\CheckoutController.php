<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Assinatura;
use App\Models\Plano;

class CheckoutController extends Controller
{
    /**
     * Show checkout page
     */
    public function index()
    {
        $user = auth()->user();

        // Verificar se o usuário completou as etapas anteriores
        if (!$user->onboarding_completed) {
            return redirect()->route('paciente.onboarding')
                ->with('warning', 'Complete seu perfil médico primeiro.');
        }

        if (!$user->plan_selected) {
            return redirect()->route('paciente.planos')
                ->with('warning', 'Selecione um plano primeiro.');
        }

        // Buscar assinatura mais recente do usuário
        $assinatura = Assinatura::where('user_id', $user->id)
            ->with('plano')
            ->latest()
            ->first();

        if (!$assinatura) {
            return redirect()->route('paciente.planos')
                ->with('error', 'Nenhum plano encontrado. Selecione um plano novamente.');
        }

        return Inertia::render('paciente/checkout', [
            'user' => $user,
            'assinatura' => $assinatura,
            'plano' => $assinatura->plano,
            'isDev' => app()->environment('local', 'development'),
        ]);
    }

    /**
     * Process checkout
     */
    public function process(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'payment_method' => 'required|in:credit_card,pix,boleto',
            'terms_accepted' => 'required|accepted',
        ]);

        // Buscar assinatura
        $assinatura = Assinatura::where('user_id', $user->id)
            ->latest()
            ->first();

        if (!$assinatura) {
            return back()->with('error', 'Assinatura não encontrada.');
        }

        // Em desenvolvimento, simular pagamento aprovado
        if (app()->environment('local', 'development')) {
            return $this->simulatePayment($user, $assinatura, $request->payment_method);
        }

        // Em produção, redirecionar para gateway de pagamento real
        return $this->processRealPayment($user, $assinatura, $request->payment_method);
    }

    /**
     * Simulate payment for development
     */
    private function simulatePayment($user, $assinatura, $paymentMethod)
    {
        // Marcar checkout como completo
        $user->update([
            'checkout_completed' => true,
            'checkout_completed_at' => now(),
            'has_subscription' => true,
        ]);

        // Ativar assinatura
        $assinatura->update([
            'status' => 'ativa',
            'start_date' => now(),
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
        ]);

        // Simular abertura de nova aba para "pagamento"
        $simulationUrl = route('paciente.checkout.simulation', [
            'assinatura' => $assinatura->id,
            'method' => $paymentMethod
        ]);

        return redirect()->route('paciente.checkout.success')
            ->with('success', 'Plano ativado com sucesso!')
            ->with('simulation_url', $simulationUrl);
    }

    /**
     * Process real payment
     */
    private function processRealPayment($user, $assinatura, $paymentMethod)
    {
        // Aqui seria implementada a integração com gateway real
        // Por enquanto, redirecionar para simulação
        return $this->simulatePayment($user, $assinatura, $paymentMethod);
    }

    /**
     * Show simulation page (opens in new tab in dev)
     */
    public function simulation(Request $request, Assinatura $assinatura)
    {
        if (!app()->environment('local', 'development')) {
            abort(404);
        }

        return Inertia::render('paciente/checkout-simulation', [
            'assinatura' => $assinatura->load('plano'),
            'payment_method' => $request->get('method', 'credit_card'),
        ]);
    }

    /**
     * Show success page
     */
    public function success()
    {
        $user = auth()->user();

        return Inertia::render('paciente/checkout-success', [
            'user' => $user,
        ]);
    }

    /**
     * Confirm and redirect to dashboard
     */
    public function confirm()
    {
        return redirect()->route('paciente.dashboard')
            ->with('success', 'Bem-vindo à plataforma! Seu plano está ativo.');
    }
}

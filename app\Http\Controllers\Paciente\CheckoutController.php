<?php

namespace App\Http\Controllers\Paciente;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Assinatura;
use App\Models\Plano;
use App\Models\Pagamento;
use App\Services\MercadoPagoService;

class CheckoutController extends Controller
{
    protected $mercadoPagoService;

    public function __construct(MercadoPagoService $mercadoPagoService)
    {
        $this->mercadoPagoService = $mercadoPagoService;
    }

    /**
     * Show checkout page
     */
    public function index()
    {
        $user = auth()->user();

        // Verificar se o usuário completou as etapas anteriores
        if (!$user->onboarding_completed) {
            return redirect()->route('paciente.onboarding')
                ->with('warning', 'Complete seu perfil médico primeiro.');
        }

        if (!$user->plan_selected) {
            return redirect()->route('paciente.planos')
                ->with('warning', 'Selecione um plano primeiro.');
        }

        // Buscar assinatura mais recente do usuário
        $assinatura = Assinatura::where('user_id', $user->id)
            ->with('plano')
            ->latest()
            ->first();

        if (!$assinatura) {
            return redirect()->route('paciente.planos')
                ->with('error', 'Nenhum plano encontrado. Selecione um plano novamente.');
        }

        return Inertia::render('paciente/checkout', [
            'user' => $user,
            'assinatura' => $assinatura,
            'plano' => $assinatura->plano,
            'isDev' => app()->environment('local', 'development'),
        ]);
    }

    /**
     * Process checkout
     */
    public function process(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'payment_method' => 'required|in:credit_card,pix,boleto',
            'terms_accepted' => 'required|accepted',
        ]);

        // Buscar assinatura
        $assinatura = Assinatura::where('user_id', $user->id)
            ->latest()
            ->first();

        if (!$assinatura) {
            return back()->with('error', 'Assinatura não encontrada.');
        }

        // Em desenvolvimento, simular pagamento aprovado
        if (app()->environment('local', 'development')) {
            return $this->simulatePayment($user, $assinatura, $request->payment_method);
        }

        // Em produção, redirecionar para gateway de pagamento real
        return $this->processRealPayment($user, $assinatura, $request->payment_method);
    }

    /**
     * Simulate payment for development
     */
    private function simulatePayment($user, $assinatura, $paymentMethod)
    {
        // Marcar checkout como completo
        $user->update([
            'checkout_completed' => true,
            'checkout_completed_at' => now(),
            'has_subscription' => true,
        ]);

        // Ativar assinatura
        $assinatura->update([
            'status' => 'ativa',
            'start_date' => now(),
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
        ]);

        // Simular abertura de nova aba para "pagamento"
        $simulationUrl = route('paciente.checkout.simulation', [
            'assinatura' => $assinatura->id,
            'method' => $paymentMethod
        ]);

        return redirect()->route('paciente.checkout.success')
            ->with('success', 'Plano ativado com sucesso!')
            ->with('simulation_url', $simulationUrl);
    }

    /**
     * Process real payment
     */
    private function processRealPayment($user, $assinatura, $paymentMethod)
    {
        // Criar pagamento no banco de dados
        $pagamento = Pagamento::create([
            'assinatura_id' => $assinatura->id,
            'amount' => $assinatura->plano->price,
            'status' => 'pendente',
            'data_vencimento' => now(),
            'forma_pagamento' => $paymentMethod,
        ]);

        // Preparar dados para o Mercado Pago
        $preferenceData = [
            'title' => 'Plano ' . $assinatura->plano->name . ' - F4 Fisio',
            'description' => 'Assinatura mensal - ' . $assinatura->plano->description,
            'amount' => $assinatura->plano->price,
            'external_reference' => $pagamento->id,
            'payer' => [
                'name' => $user->name,
                'email' => $user->email,
            ],
            'success_url' => route('paciente.checkout.success') . '?payment_id=' . $pagamento->id,
            'failure_url' => route('paciente.pagamentos.failure') . '?payment_id=' . $pagamento->id,
            'pending_url' => route('paciente.pagamentos.pending') . '?payment_id=' . $pagamento->id,
            'notification_url' => route('mercadopago.webhook'),
        ];

        // Criar preferência no Mercado Pago
        $preference = $this->mercadoPagoService->createPreference($preferenceData);

        if (!$preference['success']) {
            if (isset($preference['redirect_whatsapp']) && $preference['redirect_whatsapp']) {
                // Redirecionar para WhatsApp se MP não estiver configurado
                $whatsappUrl = config('app.whatsapp_url', 'https://wa.me/5511978196207');
                $message = urlencode('Olá! Gostaria de realizar o pagamento da minha assinatura.');
                return redirect($whatsappUrl . '?text=' . $message);
            }

            return back()->with('error', $preference['message']);
        }

        // Atualizar pagamento com dados da preferência
        $pagamento->update([
            'transaction_id' => $preference['preference_id'],
            'gateway_response' => $preference,
        ]);

        // Redirecionar para o Mercado Pago
        $redirectUrl = config('services.mercadopago.sandbox', true)
            ? $preference['sandbox_init_point'] ?? $preference['init_point']
            : $preference['init_point'];

        return redirect($redirectUrl);
    }

    /**
     * Show simulation page (opens in new tab in dev)
     */
    public function simulation(Request $request, Assinatura $assinatura)
    {
        if (!app()->environment('local', 'development')) {
            abort(404);
        }

        return Inertia::render('paciente/checkout-simulation', [
            'assinatura' => $assinatura->load('plano'),
            'payment_method' => $request->get('method', 'credit_card'),
        ]);
    }

    /**
     * Show success page
     */
    public function success(Request $request)
    {
        $user = auth()->user();
        $paymentId = $request->get('payment_id');

        // Se estiver em desenvolvimento, considerar como pago automaticamente
        if (app()->environment('local', 'development')) {
            if ($paymentId) {
                $pagamento = Pagamento::find($paymentId);
                if ($pagamento && $pagamento->status === 'pendente') {
                    $pagamento->update(['status' => 'pago']);

                    // Ativar assinatura
                    $assinatura = $pagamento->assinatura;
                    $assinatura->update([
                        'status' => 'ativa',
                        'start_date' => now(),
                        'current_period_start' => now(),
                        'current_period_end' => now()->addMonth(),
                    ]);

                    // Marcar usuário como tendo assinatura
                    $user->update([
                        'has_subscription' => true,
                        'checkout_completed' => true,
                        'checkout_completed_at' => now(),
                    ]);
                }
            }
        } else {
            // Em produção, validar pagamento via API do Mercado Pago
            if ($paymentId) {
                $this->validatePaymentStatus($paymentId);
            }
        }

        return Inertia::render('paciente/checkout-success', [
            'user' => $user,
            'payment_id' => $paymentId,
        ]);
    }

    /**
     * Validate payment status with Mercado Pago API
     */
    private function validatePaymentStatus($paymentId)
    {
        $pagamento = Pagamento::find($paymentId);
        if (!$pagamento || !$pagamento->transaction_id) {
            return;
        }

        // Buscar informações do pagamento no Mercado Pago
        $paymentInfo = $this->mercadoPagoService->getPayment($pagamento->transaction_id);

        if ($paymentInfo && isset($paymentInfo['status'])) {
            $status = $paymentInfo['status'];

            // Mapear status do Mercado Pago para nosso sistema
            $newStatus = match($status) {
                'approved' => 'pago',
                'pending', 'in_process' => 'pendente',
                'rejected', 'cancelled' => 'falhou',
                default => $pagamento->status
            };

            if ($newStatus !== $pagamento->status) {
                $pagamento->update(['status' => $newStatus]);

                // Se foi aprovado, ativar assinatura
                if ($newStatus === 'pago') {
                    $assinatura = $pagamento->assinatura;
                    $assinatura->update([
                        'status' => 'ativa',
                        'start_date' => now(),
                        'current_period_start' => now(),
                        'current_period_end' => now()->addMonth(),
                    ]);

                    // Marcar usuário como tendo assinatura
                    $pagamento->assinatura->user->update([
                        'has_subscription' => true,
                        'checkout_completed' => true,
                        'checkout_completed_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Confirm and redirect to dashboard
     */
    public function confirm()
    {
        return redirect()->route('paciente.dashboard')
            ->with('success', 'Bem-vindo à plataforma! Seu plano está ativo.');
    }
}

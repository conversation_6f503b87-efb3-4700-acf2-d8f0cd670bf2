import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Head, usePage } from '@inertiajs/react';
import { CheckCircle, CreditCard, FileText, Smartphone } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Props {
    assinatura: any;
    payment_method: string;
}

export default function CheckoutSimulation() {
    const pageProps = usePage().props as any;
    const { assinatura, payment_method } = pageProps;
    const [processing, setProcessing] = useState(true);
    const [completed, setCompleted] = useState(false);

    useEffect(() => {
        // Simular processamento de pagamento
        const timer = setTimeout(() => {
            setProcessing(false);
            setCompleted(true);
        }, 3000);

        return () => clearTimeout(timer);
    }, []);

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    const getPaymentMethodInfo = (method: string) => {
        switch (method) {
            case 'credit_card':
                return {
                    name: 'Cartão de Crédito',
                    icon: CreditCard,
                    description: 'Pagamento processado instantaneamente',
                };
            case 'pix':
                return {
                    name: 'PIX',
                    icon: Smartphone,
                    description: 'Pagamento via PIX processado',
                };
            case 'boleto':
                return {
                    name: 'Boleto Bancário',
                    icon: FileText,
                    description: 'Boleto gerado com sucesso',
                };
            default:
                return {
                    name: 'Pagamento',
                    icon: CreditCard,
                    description: 'Processando pagamento',
                };
        }
    };

    const paymentInfo = getPaymentMethodInfo(payment_method);
    const Icon = paymentInfo.icon;

    const handleClose = () => {
        window.close();
    };

    return (
        <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
            <Head title="Simulação de Pagamento" />

            <Card className="w-full max-w-md">
                <CardHeader className="text-center">
                    <div className="mx-auto mb-4 w-fit rounded-full bg-blue-100 p-3">
                        <Icon className="h-8 w-8 text-blue-600" />
                    </div>
                    <CardTitle>Simulação de Pagamento</CardTitle>
                    <CardDescription>Esta é uma simulação para demonstração</CardDescription>
                </CardHeader>

                <CardContent className="space-y-6">
                    {/* Informações do Plano */}
                    <div className="rounded-lg bg-gray-50 p-4">
                        <div className="mb-2 flex items-start justify-between">
                            <div>
                                <h3 className="font-semibold">{assinatura.plano.nome}</h3>
                                <p className="text-sm text-gray-600">{assinatura.plano.descricao}</p>
                            </div>
                            <Badge variant="secondary">{assinatura.plano.sessoes_mes} sessões</Badge>
                        </div>
                        <div className="mt-3 flex items-center justify-between">
                            <span className="text-sm text-gray-600">Valor:</span>
                            <span className="text-lg font-bold">{formatCurrency(assinatura.monthly_price)}</span>
                        </div>
                    </div>

                    {/* Status do Pagamento */}
                    <div className="text-center">
                        {processing ? (
                            <div className="space-y-3">
                                <div className="mx-auto h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent"></div>
                                <div>
                                    <p className="font-medium">Processando Pagamento</p>
                                    <p className="text-sm text-gray-600">Aguarde enquanto processamos seu {paymentInfo.name.toLowerCase()}...</p>
                                </div>
                            </div>
                        ) : completed ? (
                            <div className="space-y-3">
                                <div className="mx-auto w-fit rounded-full bg-green-100 p-2">
                                    <CheckCircle className="h-8 w-8 text-green-600" />
                                </div>
                                <div>
                                    <p className="font-medium text-green-800">Pagamento Aprovado!</p>
                                    <p className="text-sm text-gray-600">{paymentInfo.description}</p>
                                </div>
                            </div>
                        ) : null}
                    </div>

                    {/* Método de Pagamento */}
                    <div className="rounded-lg border p-3">
                        <div className="flex items-center gap-3">
                            <Icon className="h-5 w-5 text-gray-600" />
                            <div>
                                <p className="font-medium">{paymentInfo.name}</p>
                                <p className="text-sm text-gray-600">
                                    {payment_method === 'credit_card' && 'Cartão final ****1234'}
                                    {payment_method === 'pix' && 'Chave PIX: <EMAIL>'}
                                    {payment_method === 'boleto' && 'Código: 12345.67890.12345.678901'}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Informações Adicionais */}
                    <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                        <h4 className="mb-2 font-medium text-blue-800">Modo Desenvolvimento</h4>
                        <p className="text-sm text-blue-700">
                            Esta é uma simulação de pagamento. Em produção, você seria redirecionado para o gateway de pagamento real (Mercado Pago,
                            PagSeguro, etc.).
                        </p>
                    </div>

                    {/* Próximos Passos */}
                    {completed && (
                        <div className="space-y-3">
                            <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                                <h4 className="mb-2 font-medium text-green-800">Próximos Passos</h4>
                                <ul className="space-y-1 text-sm text-green-700">
                                    <li>✓ Plano ativado com sucesso</li>
                                    <li>✓ Acesso liberado à plataforma</li>
                                    <li>✓ E-mail de confirmação enviado</li>
                                    <li>✓ Você pode começar a agendar sessões</li>
                                </ul>
                            </div>

                            <Button onClick={handleClose} className="w-full">
                                Fechar Simulação
                            </Button>
                        </div>
                    )}

                    {/* Informações de Desenvolvimento */}
                    <div className="text-center text-xs text-gray-500">
                        <p>Simulação criada para demonstração do fluxo</p>
                        <p>ID da Assinatura: {assinatura.id}</p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

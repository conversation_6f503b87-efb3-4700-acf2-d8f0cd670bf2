<?php

// Script para testar o fluxo completo
$baseUrl = 'http://localhost:8000';

echo "🚀 Testando fluxo completo de onboarding...\n\n";

// 1. Fazer logout primeiro
echo "1. Fazendo logout...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/logout');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');

$logoutResponse = curl_exec($ch);
$logoutHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
echo "   Logout HTTP Code: $logoutHttpCode\n";
curl_close($ch);

// 2. Acessar página de login
echo "\n2. Acessando página de login...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');

$loginPage = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
echo "   Login page HTTP Code: $httpCode\n";

if ($httpCode == 200 && $loginPage) {
    // 3. Extrair token CSRF e fazer login
    echo "\n3. Fazendo login...\n";
    preg_match('/<meta name="csrf-token" content="([^"]+)"/', $loginPage, $matches);
    if (isset($matches[1])) {
        $csrfToken = $matches[1];
        
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            '_token' => $csrfToken,
            'email' => '<EMAIL>',
            'password' => 'password123',
            'remember' => false
        ]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'X-CSRF-TOKEN: ' . $csrfToken
        ]);

        $loginResponse = curl_exec($ch);
        $loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        echo "   Login HTTP Code: $loginHttpCode\n";
        
        if ($loginHttpCode >= 300 && $loginHttpCode < 400) {
            $dashboardUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
            echo "   Redirecionado para: $dashboardUrl\n";
            
            // 4. Seguir redirecionamento do dashboard
            echo "\n4. Acessando dashboard...\n";
            curl_setopt($ch, CURLOPT_URL, $dashboardUrl);
            curl_setopt($ch, CURLOPT_POST, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, '');
            curl_setopt($ch, CURLOPT_HTTPHEADER, []);
            
            $dashboardResponse = curl_exec($ch);
            $dashboardHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            echo "   Dashboard HTTP Code: $dashboardHttpCode\n";
            
            if ($dashboardHttpCode >= 300 && $dashboardHttpCode < 400) {
                $finalUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
                echo "   Redirecionado para: $finalUrl\n";
                
                // 5. Acessar página final
                echo "\n5. Acessando página final...\n";
                curl_setopt($ch, CURLOPT_URL, $finalUrl);
                
                $finalResponse = curl_exec($ch);
                $finalHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                echo "   Final HTTP Code: $finalHttpCode\n";
                
                if ($finalHttpCode == 200) {
                    echo "   ✅ Sucesso! Usuário acessou: $finalUrl\n";
                    
                    // Verificar se é a página esperada
                    if (strpos($finalUrl, '/paciente/dashboard') !== false) {
                        echo "   🎉 Usuário chegou ao dashboard do paciente!\n";
                    } elseif (strpos($finalUrl, '/paciente/onboarding') !== false) {
                        echo "   📝 Usuário foi para onboarding (esperado se não completou)\n";
                    } elseif (strpos($finalUrl, '/paciente/planos') !== false) {
                        echo "   💳 Usuário foi para seleção de planos\n";
                    } elseif (strpos($finalUrl, '/paciente/checkout') !== false) {
                        echo "   🛒 Usuário foi para checkout\n";
                    }
                } else {
                    echo "   ❌ Erro ao acessar página final\n";
                }
            } elseif ($dashboardHttpCode == 200) {
                echo "   ✅ Dashboard acessado diretamente!\n";
            }
        }
    }
}

curl_close($ch);

// Limpar cookies
if (file_exists('cookies.txt')) {
    unlink('cookies.txt');
}

echo "\n🏁 Teste concluído!\n";

<?php

namespace App\Http\Controllers;

use App\Models\Pagamento;
use App\Services\MercadoPagoService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class MercadoPagoSimulatorController extends Controller
{
    protected $mercadoPagoService;

    public function __construct(MercadoPagoService $mercadoPagoService)
    {
        $this->mercadoPagoService = $mercadoPagoService;
    }

    /**
     * Show payment simulation page
     */
    public function paymentPage($transactionId)
    {
        if (!app()->environment('local', 'development')) {
            abort(404);
        }

        $pagamento = Pagamento::where('transaction_id', $transactionId)->first();
        
        if (!$pagamento) {
            abort(404, 'Pagamento não encontrado');
        }

        return Inertia::render('Simulator/MercadoPagoPayment', [
            'pagamento' => $pagamento->load('assinatura.plano', 'assinatura.user'),
            'transaction_id' => $transactionId,
        ]);
    }

    /**
     * Process simulated payment
     */
    public function processPayment(Request $request, $transactionId)
    {
        if (!app()->environment('local', 'development')) {
            abort(404);
        }

        $request->validate([
            'action' => 'required|in:approve,reject,cancel'
        ]);

        $pagamento = Pagamento::where('transaction_id', $transactionId)->first();
        
        if (!$pagamento) {
            return response()->json(['error' => 'Pagamento não encontrado'], 404);
        }

        $action = $request->input('action');
        
        // Simular webhook do Mercado Pago
        $this->simulateWebhook($pagamento, $action);

        // Redirecionar baseado na ação
        switch ($action) {
            case 'approve':
                return redirect()->route('mercadopago.simulator.success', $transactionId);
            case 'reject':
            case 'cancel':
                return redirect()->route('mercadopago.simulator.failure', $transactionId);
        }
    }

    /**
     * Show success page
     */
    public function success($transactionId)
    {
        if (!app()->environment('local', 'development')) {
            abort(404);
        }

        $pagamento = Pagamento::where('transaction_id', $transactionId)->first();
        
        return Inertia::render('Simulator/PaymentSuccess', [
            'pagamento' => $pagamento->load('assinatura.plano', 'assinatura.user'),
            'transaction_id' => $transactionId,
        ]);
    }

    /**
     * Show failure page
     */
    public function failure($transactionId)
    {
        if (!app()->environment('local', 'development')) {
            abort(404);
        }

        $pagamento = Pagamento::where('transaction_id', $transactionId)->first();
        
        return Inertia::render('Simulator/PaymentFailure', [
            'pagamento' => $pagamento->load('assinatura.plano', 'assinatura.user'),
            'transaction_id' => $transactionId,
        ]);
    }

    /**
     * Simulate webhook call
     */
    private function simulateWebhook($pagamento, $action)
    {
        $status = match($action) {
            'approve' => 'approved',
            'reject' => 'rejected',
            'cancel' => 'cancelled',
            default => 'pending'
        };

        // Simular dados do webhook
        $webhookData = [
            'type' => 'payment',
            'data' => [
                'id' => 'sim_' . $pagamento->id . '_' . time()
            ]
        ];

        // Simular resposta da API do Mercado Pago
        $paymentData = [
            'id' => $webhookData['data']['id'],
            'status' => $status,
            'external_reference' => $pagamento->id,
            'transaction_amount' => $pagamento->amount,
            'payment_method_id' => 'visa',
            'date_created' => now()->toISOString(),
        ];

        Log::info('🎭 [SIMULADOR] Simulando webhook Mercado Pago', [
            'transaction_id' => $pagamento->transaction_id,
            'pagamento_id' => $pagamento->id,
            'action' => $action,
            'status' => $status,
            'webhook_data' => $webhookData,
            'payment_data' => $paymentData
        ]);

        // Processar como se fosse um webhook real
        $this->processSimulatedWebhook($pagamento, $paymentData);
    }

    /**
     * Process simulated webhook data
     */
    private function processSimulatedWebhook($pagamento, $paymentData)
    {
        $status = $paymentData['status'];
        
        // Mapear status do Mercado Pago para nosso sistema
        $newStatus = match($status) {
            'approved' => 'pago',
            'pending', 'in_process' => 'pendente',
            'rejected', 'cancelled' => 'falhou',
            default => $pagamento->status
        };

        if ($newStatus !== $pagamento->status) {
            $pagamento->update([
                'status' => $newStatus,
                'gateway_response' => $paymentData,
                'paid_at' => $newStatus === 'pago' ? now() : null,
            ]);

            Log::info('🎭 [SIMULADOR] Status do pagamento atualizado', [
                'pagamento_id' => $pagamento->id,
                'old_status' => $pagamento->getOriginal('status'),
                'new_status' => $newStatus,
            ]);

            // Se foi aprovado, ativar assinatura
            if ($newStatus === 'pago') {
                $assinatura = $pagamento->assinatura;
                $assinatura->update([
                    'status' => 'ativa',
                    'start_date' => now(),
                    'current_period_start' => now(),
                    'current_period_end' => now()->addMonth(),
                ]);

                // Marcar usuário como tendo assinatura
                $assinatura->user->update([
                    'has_subscription' => true,
                    'checkout_completed' => true,
                    'checkout_completed_at' => now(),
                ]);

                Log::info('🎭 [SIMULADOR] Assinatura ativada', [
                    'assinatura_id' => $assinatura->id,
                    'user_id' => $assinatura->user_id,
                    'plano_id' => $assinatura->plano_id,
                ]);
            }
        }
    }
}

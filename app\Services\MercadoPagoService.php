<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MercadoPagoService
{
    private $accessToken;
    private $baseUrl;

    public function __construct()
    {
        $this->accessToken = config('services.mercadopago.access_token');
        $this->baseUrl = 'https://api.mercadopago.com';
    }

    /**
     * Criar preferência de pagamento
     */
    public function createPreference($data)
    {
        if (!$this->accessToken) {
            return [
                'success' => false,
                'message' => 'Mercado Pago não configurado. Entre em contato via WhatsApp.',
                'redirect_whatsapp' => true
            ];
        }

        try {
            $httpClient = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ]);

            // Em desenvolvimento, desabilitar verificação SSL
            if (config('app.env') === 'local') {
                $httpClient = $httpClient->withOptions([
                    'verify' => false,
                ]);
            }

            $response = $httpClient->post($this->baseUrl . '/checkout/preferences', [
                'items' => [
                    [
                        'title' => $data['title'],
                        'description' => $data['description'] ?? '',
                        'quantity' => 1,
                        'currency_id' => 'BRL',
                        'unit_price' => (float) $data['amount'],
                    ]
                ],
                'payer' => [
                    'name' => $data['payer']['name'] ?? '',
                    'email' => $data['payer']['email'] ?? '',
                ],
                'back_urls' => [
                    'success' => $data['success_url'] ?? url('/paciente/pagamentos/success'),
                    'failure' => $data['failure_url'] ?? url('/paciente/pagamentos/failure'),
                    'pending' => $data['pending_url'] ?? url('/paciente/pagamentos/pending'),
                ],
                'external_reference' => $data['external_reference'] ?? null,
                'notification_url' => $data['notification_url'] ?? url('/webhook/mercadopago'),
            ]);

            if ($response->successful()) {
                $preference = $response->json();
                return [
                    'success' => true,
                    'preference_id' => $preference['id'],
                    'init_point' => $preference['init_point'],
                    'sandbox_init_point' => $preference['sandbox_init_point'] ?? null,
                ];
            }

            Log::error('Erro ao criar preferência Mercado Pago', [
                'response' => $response->json(),
                'status' => $response->status()
            ]);

            return [
                'success' => false,
                'message' => 'Erro ao processar pagamento. Tente novamente.',
            ];

        } catch (\Exception $e) {
            Log::error('Exceção ao criar preferência Mercado Pago', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Erro interno. Entre em contato via WhatsApp.',
                'redirect_whatsapp' => true
            ];
        }
    }

    /**
     * Obter informações de um pagamento
     */
    public function getPayment($paymentId)
    {
        if (!$this->accessToken) {
            return null;
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
            ])->get($this->baseUrl . '/v1/payments/' . $paymentId);

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Erro ao obter pagamento Mercado Pago', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Verificar se o Mercado Pago está configurado
     */
    public function isConfigured()
    {
        return !empty($this->accessToken);
    }

    /**
     * Processar webhook do Mercado Pago
     */
    public function processWebhook($data)
    {
        try {
            if (isset($data['type']) && $data['type'] === 'payment') {
                $paymentId = $data['data']['id'] ?? null;
                
                if ($paymentId) {
                    $paymentInfo = $this->getPayment($paymentId);
                    
                    if ($paymentInfo) {
                        // Processar atualização do pagamento
                        return $this->updatePaymentStatus($paymentInfo);
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Erro ao processar webhook Mercado Pago', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Atualizar status do pagamento baseado na resposta do MP
     */
    private function updatePaymentStatus($paymentInfo)
    {
        $externalReference = $paymentInfo['external_reference'] ?? null;
        $status = $paymentInfo['status'] ?? null;

        if (!$externalReference || !$status) {
            return false;
        }

        try {
            // Encontrar o pagamento no banco
            $pagamento = \App\Models\Pagamento::find($externalReference);

            if (!$pagamento) {
                Log::warning('Pagamento não encontrado no banco', [
                    'external_reference' => $externalReference
                ]);
                return false;
            }

            // Mapear status do Mercado Pago
            $newStatus = $this->mapMercadoPagoStatus($status);

            // Preparar dados para atualização
            $updateData = [
                'status' => $newStatus,
                'gateway_response' => $paymentInfo,
            ];

            // Se foi aprovado, definir data de pagamento e método
            if ($newStatus === 'pago') {
                $updateData['paid_at'] = now();
                $updateData['method'] = $this->mapPaymentMethod($paymentInfo['payment_method_id'] ?? null);
            }

            // Atualizar o pagamento
            $pagamento->update($updateData);

            Log::info('Pagamento atualizado via webhook', [
                'pagamento_id' => $pagamento->id,
                'external_reference' => $externalReference,
                'old_status' => $pagamento->getOriginal('status'),
                'new_status' => $newStatus,
                'mp_payment_id' => $paymentInfo['id']
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Erro ao atualizar pagamento via webhook', [
                'external_reference' => $externalReference,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Mapear status do Mercado Pago para status interno
     */
    private function mapMercadoPagoStatus($mpStatus)
    {
        switch ($mpStatus) {
            case 'approved':
                return 'pago';
            case 'pending':
            case 'in_process':
                return 'pendente';
            case 'rejected':
            case 'cancelled':
                return 'falhou';
            default:
                return 'pendente';
        }
    }

    /**
     * Mapear método de pagamento do Mercado Pago
     */
    private function mapPaymentMethod($mpMethod)
    {
        if (!$mpMethod) return null;

        $methodMap = [
            'pix' => 'pix',
            'bolbradesco' => 'boleto',
            'visa' => 'cartao_credito',
            'master' => 'cartao_credito',
            'amex' => 'cartao_credito',
            'elo' => 'cartao_credito',
            'hipercard' => 'cartao_credito',
            'debvisa' => 'cartao_debito',
            'debmaster' => 'cartao_debito',
        ];

        return $methodMap[$mpMethod] ?? 'cartao_credito';
    }



}

<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$user = App\Models\User::where('email', 'rafael<PERSON><PERSON><PERSON>@gmail.com')->first();

if ($user) {
    echo "Resetting onboarding for user: " . $user->name . PHP_EOL;
    
    $user->update([
        'onboarding_completed' => false,
        'onboarding_completed_at' => null,
        // Manter dados pessoais básicos mas limpar dados médicos específicos
        'medical_history' => null,
        'emergency_contact' => null,
        'main_objective' => null,
        'pain_level' => '5', // Valor padrão
        'specific_areas' => null,
        'treatment_goals' => null,
        'preferred_time' => null,
        'preferred_days' => null,
        'communication_preference' => 'whatsapp',
        'reminder_frequency' => 'daily',
    ]);
    
    echo "Onboarding reset successfully!" . PHP_EOL;
    echo "User can now complete onboarding again." . PHP_EOL;
} else {
    echo "User not found" . PHP_EOL;
}

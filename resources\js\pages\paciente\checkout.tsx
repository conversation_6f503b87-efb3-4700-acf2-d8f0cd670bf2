import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import PacienteLayout from '@/layouts/PacienteLayout';
import { Head, useForm, usePage } from '@inertiajs/react';
import { CheckCircle, Clock, CreditCard, FileText, Shield, Smartphone } from 'lucide-react';
import React, { useState } from 'react';

interface Props {
    user: any;
    assinatura: any;
    plano: any;
    isDev: boolean;
}

export default function Checkout() {
    const pageProps = usePage().props as any;
    const { user, assinatura, plano, isDev } = pageProps;
    const [termsAccepted, setTermsAccepted] = useState(false);

    const { data, setData, post, processing, errors } = useForm({
        payment_method: 'credit_card',
        terms_accepted: false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setData('terms_accepted', termsAccepted as any);
        post(route('paciente.checkout.process'));
    };

    const paymentMethods = [
        {
            id: 'credit_card',
            name: 'Cartão de Crédito',
            description: 'Pagamento instantâneo',
            icon: CreditCard,
        },
        {
            id: 'pix',
            name: 'PIX',
            description: 'Pagamento instantâneo via PIX',
            icon: Smartphone,
        },
        {
            id: 'boleto',
            name: 'Boleto Bancário',
            description: 'Vencimento em 3 dias úteis',
            icon: FileText,
        },
    ];

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
        }).format(value);
    };

    return (
        <PacienteLayout>
            <Head title="Finalizar Plano" />

            <div className="container mx-auto max-w-4xl px-4 py-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900">Finalizar Plano</h1>
                    <p className="mt-2 text-gray-600">Confirme os detalhes do seu plano e finalize sua assinatura</p>
                </div>

                {isDev && (
                    <div className="mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4">
                        <div className="flex items-center gap-2">
                            <Shield className="h-5 w-5 text-yellow-600" />
                            <span className="font-medium text-yellow-800">Modo Desenvolvimento</span>
                        </div>
                        <p className="mt-1 text-yellow-700">O pagamento será simulado. Uma nova aba será aberta para demonstração.</p>
                    </div>
                )}

                <div className="grid gap-8 lg:grid-cols-3">
                    {/* Resumo do Plano */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Resumo do Plano</CardTitle>
                                <CardDescription>Detalhes da sua assinatura</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h3 className="text-lg font-semibold">{plano.nome}</h3>
                                        <p className="text-gray-600">{plano.descricao}</p>
                                    </div>
                                    <Badge variant="secondary">{plano.sessoes_mes} sessões/mês</Badge>
                                </div>

                                <Separator />

                                <div className="space-y-3">
                                    <div className="flex justify-between">
                                        <span>Valor mensal</span>
                                        <span className="font-semibold">{formatCurrency(assinatura.monthly_price)}</span>
                                    </div>
                                    <div className="flex justify-between text-sm text-gray-600">
                                        <span>Período de cobrança</span>
                                        <span>Mensal</span>
                                    </div>
                                </div>

                                <Separator />

                                <div className="flex justify-between text-lg font-bold">
                                    <span>Total</span>
                                    <span>{formatCurrency(assinatura.monthly_price)}</span>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Método de Pagamento */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Método de Pagamento</CardTitle>
                                <CardDescription>Escolha como deseja pagar</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <RadioGroup value={data.payment_method} onValueChange={(value) => setData('payment_method', value)}>
                                        {paymentMethods.map((method) => {
                                            const Icon = method.icon;
                                            return (
                                                <div key={method.id} className="flex items-center space-x-3 rounded-lg border p-4 hover:bg-gray-50">
                                                    <RadioGroupItem value={method.id} id={method.id} />
                                                    <Icon className="h-5 w-5 text-gray-600" />
                                                    <Label htmlFor={method.id} className="flex-1 cursor-pointer">
                                                        <div>
                                                            <div className="font-medium">{method.name}</div>
                                                            <div className="text-sm text-gray-600">{method.description}</div>
                                                        </div>
                                                    </Label>
                                                </div>
                                            );
                                        })}
                                    </RadioGroup>

                                    {errors.payment_method && <p className="text-sm text-red-600">{errors.payment_method}</p>}

                                    <div className="flex items-start space-x-3">
                                        <Checkbox
                                            id="terms"
                                            checked={termsAccepted}
                                            onCheckedChange={(checked) => setTermsAccepted(checked === true)}
                                        />
                                        <Label htmlFor="terms" className="cursor-pointer text-sm leading-relaxed">
                                            Eu aceito os{' '}
                                            <a href="#" className="text-blue-600 hover:underline">
                                                termos de uso
                                            </a>{' '}
                                            e a{' '}
                                            <a href="#" className="text-blue-600 hover:underline">
                                                política de privacidade
                                            </a>
                                        </Label>
                                    </div>

                                    {errors.terms_accepted && <p className="text-sm text-red-600">{errors.terms_accepted}</p>}

                                    <Button type="submit" className="w-full" disabled={!termsAccepted || processing}>
                                        {processing ? (
                                            <>
                                                <Clock className="mr-2 h-4 w-4 animate-spin" />
                                                Processando...
                                            </>
                                        ) : (
                                            <>
                                                <CheckCircle className="mr-2 h-4 w-4" />
                                                Finalizar Plano
                                            </>
                                        )}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar com informações */}
                    <div>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Benefícios Inclusos</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <span className="text-sm">Fisioterapeutas qualificados</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <span className="text-sm">Atendimento domiciliar</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <span className="text-sm">Relatórios de evolução</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <span className="text-sm">Suporte 24/7</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                    <span className="text-sm">Cancelamento a qualquer momento</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle className="text-lg">Precisa de Ajuda?</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="mb-3 text-sm text-gray-600">Nossa equipe está pronta para ajudar você.</p>
                                <Button variant="outline" className="w-full" asChild>
                                    <a href="https://wa.me/5511978196207" target="_blank" rel="noopener noreferrer">
                                        Falar no WhatsApp
                                    </a>
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </PacienteLayout>
    );
}

[2025-08-09 19:27:19] local.INFO: PlanosController@store called {"user":{"App\\Models\\User":{"id":33,"name":"Teste Mercado Pago","email":"<EMAIL>","email_verified_at":null,"role":"paciente","phone":"(11) 99999-9999","birth_date":"1990-01-01T02:00:00.000000Z","gender":"masculino","address":"Rua Teste, 123","city":"São Paulo","state":"SP","zip_code":"01234-567","medical_history":"Teste de histórico médico para validação do fluxo do Mercado Pago real.","emergency_contact":null,"active":true,"created_at":"2025-08-09T22:12:36.000000Z","updated_at":"2025-08-09T22:16:41.000000Z","has_subscription":false,"avatar":null,"privacy_profile_visible":true,"privacy_contact_visible":true,"privacy_medical_visible":false,"privacy_allow_marketing":false,"main_objective":"alivio_dor","pain_level":"5","specific_areas":[],"treatment_goals":null,"preferred_time":"manha","preferred_days":[],"communication_preference":"whatsapp","reminder_frequency":"daily","onboarding_completed":true,"plan_selected":true,"checkout_completed":false,"onboarding_completed_at":"2025-08-09T22:16:08.000000Z","plan_selected_at":"2025-08-09T22:16:41.000000Z","checkout_completed_at":null,"afiliado":null}},"request_data":{"plano_type":"pessoal","current_user_mode":"normal"}} 
[2025-08-09 19:27:19] local.INFO: Processing plan selection {"user_id":33,"plano_type":"pessoal","current_has_subscription":false} 
[2025-08-09 19:27:19] local.INFO: No affiliate referral found for sale {"user_id":33,"valor_venda":180.0} 
[2025-08-09 19:27:19] local.INFO: Mercado Pago preference created {"user_id":33,"pagamento_id":46,"preference_id":"247350285-91e84eb3-0263-4d44-ba57-eeddd3b675e3","checkout_url":"https://sandbox.mercadopago.com.br/checkout/v1/redirect?pref_id=247350285-91e84eb3-0263-4d44-ba57-eeddd3b675e3"} 
[2025-08-09 19:29:38] local.INFO: PlanosController@store called {"user":{"App\\Models\\User":{"id":33,"name":"Teste Mercado Pago","email":"<EMAIL>","email_verified_at":null,"role":"paciente","phone":"(11) 99999-9999","birth_date":"1990-01-01T02:00:00.000000Z","gender":"masculino","address":"Rua Teste, 123","city":"São Paulo","state":"SP","zip_code":"01234-567","medical_history":"Teste de histórico médico para validação do fluxo do Mercado Pago real.","emergency_contact":null,"active":true,"created_at":"2025-08-09T22:12:36.000000Z","updated_at":"2025-08-09T22:27:19.000000Z","has_subscription":false,"avatar":null,"privacy_profile_visible":true,"privacy_contact_visible":true,"privacy_medical_visible":false,"privacy_allow_marketing":false,"main_objective":"alivio_dor","pain_level":"5","specific_areas":[],"treatment_goals":null,"preferred_time":"manha","preferred_days":[],"communication_preference":"whatsapp","reminder_frequency":"daily","onboarding_completed":true,"plan_selected":true,"checkout_completed":false,"onboarding_completed_at":"2025-08-09T22:16:08.000000Z","plan_selected_at":"2025-08-09T22:27:19.000000Z","checkout_completed_at":null,"afiliado":null}},"request_data":{"plano_type":null,"current_user_mode":"normal"}} 
[2025-08-09 19:30:45] local.INFO: PlanosController@store called {"user":{"App\\Models\\User":{"id":33,"name":"Teste Mercado Pago","email":"<EMAIL>","email_verified_at":null,"role":"paciente","phone":"(11) 99999-9999","birth_date":"1990-01-01T02:00:00.000000Z","gender":"masculino","address":"Rua Teste, 123","city":"São Paulo","state":"SP","zip_code":"01234-567","medical_history":"Teste de histórico médico para validação do fluxo do Mercado Pago real.","emergency_contact":null,"active":true,"created_at":"2025-08-09T22:12:36.000000Z","updated_at":"2025-08-09T22:27:19.000000Z","has_subscription":false,"avatar":null,"privacy_profile_visible":true,"privacy_contact_visible":true,"privacy_medical_visible":false,"privacy_allow_marketing":false,"main_objective":"alivio_dor","pain_level":"5","specific_areas":[],"treatment_goals":null,"preferred_time":"manha","preferred_days":[],"communication_preference":"whatsapp","reminder_frequency":"daily","onboarding_completed":true,"plan_selected":true,"checkout_completed":false,"onboarding_completed_at":"2025-08-09T22:16:08.000000Z","plan_selected_at":"2025-08-09T22:27:19.000000Z","checkout_completed_at":null,"afiliado":null}},"request_data":{"plano_type":null,"current_user_mode":"normal"}} 

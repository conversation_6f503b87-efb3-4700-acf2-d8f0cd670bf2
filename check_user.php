<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$user = App\Models\User::where('email', 'raf<PERSON><PERSON><PERSON><PERSON>@gmail.com')->first();

if ($user) {
    echo "User found: " . $user->name . PHP_EOL;
    echo "Email: " . $user->email . PHP_EOL;
    echo "Role: " . $user->role . PHP_EOL;
    echo "Onboarding completed: " . ($user->onboarding_completed ? 'Yes' : 'No') . PHP_EOL;
    echo "Onboarding completed at: " . ($user->onboarding_completed_at ?? 'Not set') . PHP_EOL;
    echo "Has subscription: " . ($user->has_subscription ? 'Yes' : 'No') . PHP_EOL;
    echo "Created at: " . $user->created_at . PHP_EOL;
    echo "Updated at: " . $user->updated_at . PHP_EOL;
    
    // Verificar campos específicos do onboarding
    echo "\n--- Dados do Onboarding ---" . PHP_EOL;
    echo "Phone: " . ($user->phone ?? 'Not set') . PHP_EOL;
    echo "Birth date: " . ($user->birth_date ?? 'Not set') . PHP_EOL;
    echo "Gender: " . ($user->gender ?? 'Not set') . PHP_EOL;
    echo "Address: " . ($user->address ?? 'Not set') . PHP_EOL;
    echo "City: " . ($user->city ?? 'Not set') . PHP_EOL;
    echo "State: " . ($user->state ?? 'Not set') . PHP_EOL;
    echo "ZIP code: " . ($user->zip_code ?? 'Not set') . PHP_EOL;
    echo "Medical history: " . ($user->medical_history ?? 'Not set') . PHP_EOL;
    echo "Emergency contact: " . ($user->emergency_contact ?? 'Not set') . PHP_EOL;
} else {
    echo "User not found" . PHP_EOL;
}

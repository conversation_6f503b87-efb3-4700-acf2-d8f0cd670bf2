<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // Só aplica para pacientes
        if ($user && $user->role === 'paciente' && !$user->has_subscription) {
            // Permite acesso às rotas do fluxo de onboarding, planos, checkout e configurações
            $allowedRoutes = [
                'paciente.onboarding',
                'paciente.onboarding.store',
                'paciente.onboarding.save-partial',
                'paciente.planos',
                'paciente.planos.store',
                'paciente.checkout',
                'paciente.checkout.process',
                'paciente.checkout.simulation',
                'paciente.checkout.success',
                'paciente.checkout.confirm',
                'paciente.afiliados',
                'logout',
                'profile.edit',
                'profile.update',
                'profile.destroy'
            ];

            if (!in_array($request->route()->getName(), $allowedRoutes)) {
                // Redirecionar baseado no progresso do usuário
                if (!$user->onboarding_completed) {
                    return redirect()->route('paciente.onboarding')
                        ->with('warning', 'Complete seu perfil médico para continuar.');
                } elseif (!$user->plan_selected) {
                    return redirect()->route('paciente.planos')
                        ->with('warning', 'Escolha um plano para continuar.');
                } elseif (!$user->checkout_completed) {
                    return redirect()->route('paciente.checkout')
                        ->with('warning', 'Finalize seu plano para continuar.');
                } else {
                    return redirect()->route('paciente.checkout')
                        ->with('warning', 'Complete o pagamento para ativar sua assinatura.');
                }
            }
        }

        return $next($request);
    }
}
